use std::{collections::HashMap, str::FromStr};

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub jwt: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub token_id: String,
    pub exp: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionInfo {
    pub valid_until: DateTime<Utc>,
    pub next_reset: Option<DateTime<Utc>>,
    pub traffic_used: i64,
    pub traffic_total: i64,
    pub bandwidth: Option<i32>,
    pub lines: Vec<LineInfo>,
    pub is_admin: bool,
    pub allow_forward_endpoint: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LineInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub is_online: Option<bool>,
    pub port_start: Option<i32>,
    pub port_end: Option<i32>,
    pub allow_forward: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PortInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub port_v4: i32,
    pub traffic_in: i64,
    pub traffic_out: i64,
    pub outbound_endpoint_id: Option<i32>,
    pub is_suspended: bool,

    // forward config
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // target select
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub line: Option<i32>,
    pub entry_point: Option<String>,
    pub target: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ForwardEndpointSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub ingress_address: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub token_id: Option<String>,
    pub email: Option<String>,
    pub valid_until_start: Option<String>,
    pub valid_until_end: Option<String>,
    pub next_reset_start: Option<String>,
    pub next_reset_end: Option<String>,
    pub lines: Option<Vec<i32>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServerSearchRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub id: Option<i32>,
    pub name: Option<String>,
    pub ip_addr: Option<String>,
    pub version: Option<Vec<String>>,
    pub status: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedPortsResponse {
    pub ports: Vec<PortInfo>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub current_page: u32,
    pub page_size: u32,
    pub total_items: u32,
    pub total_pages: u32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePortRequest {
    pub display_name: String,
    pub expected_port: Option<u16>,
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,

    pub outbound_endpoint_id: i32,
    pub balance_strategy: Option<u32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RmvPortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct ModifyPortRequest {
    pub id: i32, // port id
    pub display_name: String,
    pub expected_port: Option<u16>,
    pub target_address_list: Vec<String>,
    pub target_select_mode: Option<u32>,
    pub test_method: Option<u32>,

    pub balance_strategy: Option<u32>,
    pub forward_endpoints: Option<Vec<i32>>,

    // tot server
    pub tot_server_list: Option<Vec<i32>>,
    pub tot_server_select_mode: Option<u32>,
    pub tot_server_test_method: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SuspendPortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ResumePortRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NetSpeedItem {
    pub rx: u64,       // unit: byte/s
    pub tx: u64,       // unit: byte/s
    pub total_rx: u64, // unit: byte
    pub total_tx: u64, // unit: byte
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatsItem {
    pub cpu_usage: f32,    // percentage
    pub memory_total: u64, // unit: byte
    pub memory_used: u64,  // unit: byte
    pub uptime: u64,       // unit: second
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetLineStatsResponse {
    pub netcard_speed: HashMap<i32, NetSpeedItem>, // line_id -> netcard_speed
    pub system_stats: HashMap<i32, SystemStatsItem>, // line_id -> system_stats
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ForwardProtocol {
    Hammer,
}
impl ForwardProtocol {
    pub fn to_string(&self) -> String {
        match self {
            ForwardProtocol::Hammer => "hammer".to_string(),
        }
    }
}
impl FromStr for ForwardProtocol {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "hammer" => Ok(ForwardProtocol::Hammer),
            s => Err(anyhow::anyhow!("Invalid protocol: {}", s)),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ForwardEndpoint {
    pub id: i32,
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: i32,
    pub is_public: bool,
    pub token_id: String,
    pub allow_ipv6: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AddForwardEndpointRequest {
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: Option<i32>, // serve port leave empty for auto assign
    pub allow_ipv6: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ModifyForwardEndpointRequest {
    pub id: i32,
    pub name: String,
    pub ingress_address: String,
    pub protocol: ForwardProtocol,
    pub serve_port: Option<i32>,
    pub allow_ipv6: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RmvForwardEndpointRequest {
    pub id: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetForwardEndpointListResponse {
    pub forward_endpoints: Vec<ForwardEndpoint>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedForwardEndpointResponse {
    pub forward_endpoints: Vec<ForwardEndpoint>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TestLatencyRequest {
    pub port_id: i32,
}

// #[derive(Debug, Serialize, Deserialize)]
// pub struct TestLatencyResponse {
//     pub fwd_server_latency: Option<HashMap<String, String>>, // entry -> fwd: fwd_server_name -> latency
//     pub remote_latency: HashMap<String, (Option<String>, String)>, // fwd -> remote: remote_address -> (fwd_server_name, latency)
// }

#[derive(Debug, Serialize, Deserialize)]
pub struct TestLatencyResponse {
    pub content: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionItem {
    pub id: i32,
    pub email_address: String,
    pub token_id: String,
    pub valid_until: DateTime<Utc>,
    pub next_reset: Option<DateTime<Utc>>,
    pub traffic_used: i64,
    pub traffic_total: i64,
    pub lines: Vec<LineInfo>,
    pub activated: bool,
    pub allow_forward_endpoint: bool,
    pub max_ports_per_server: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubscriptionListResponse {
    pub subscriptions: Vec<SubscriptionItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedSubscriptionResponse {
    pub subscriptions: Vec<SubscriptionItem>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AddeUserInfo {
    pub address: String,
    pub bandwidth: Option<u32>, //mbps
    pub traffic: u64,           // GB
    pub activated: bool,
    pub max_ports_per_server: u32,
    pub bill_type: BillingType,
    pub total_days: u32,
    pub lines: Vec<i32>,
    pub allow_forward_endpoint: bool,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub enum BillingType {
    Cycle { days: usize, price: usize },
    OneTime { price: usize, days: usize },
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RmvUserRequest {
    pub user_id: i32,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct EditUserInfo {
    pub user_id: i32,
    pub address: String,
    pub bandwidth: Option<u32>, //mbps
    pub traffic: u64,           // GB
    pub activated: bool,
    pub max_ports_per_server: u32,
    pub bill_type: BillingType,
    pub total_days: u32,
    pub lines: Vec<i32>,
    pub allow_forward_endpoint: bool,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ExtendSubscriptionTimeRequest {
    pub user_id: i32,
    pub days: Option<i32>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ServerListResponse {
    pub servers: Vec<ServerInfo>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct PaginatedServerResponse {
    pub servers: Vec<ServerInfo>,
    pub pagination: PaginationInfo,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ServerInfo {
    pub id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub is_online: Option<bool>,
    pub port_start: Option<i32>,
    pub port_end: Option<i32>,
    pub used_ports: Option<Vec<i32>>,
    pub interface_name: Option<String>,
    pub server_pubkey: String,
    pub version: Option<String>,
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub traffic_scale: Option<f32>,    
    pub allow_forward: Option<bool>,     
    pub allow_latency_test: Option<bool>,
    pub allow_ipv6: Option<bool>,
    pub use_forward_as_tun: Option<bool>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AddServerRequest {
    pub display_name: String,
    pub ip_addr: String,
    pub interface_name: String,
    pub port_start: Option<i32>, // default 30000
    pub port_end: Option<i32>,   // default 31000
    // advanced options
    pub traffic_scale: Option<f32>,       // default 1.0
    pub allow_forward: Option<bool>,      // default false
    pub allow_latency_test: Option<bool>, // default true
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub allow_ipv6: Option<bool>, // default false
    pub use_forward_as_tun: Option<bool>, // default false
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Default)]
pub struct ModifyServerRequest {
    pub server_id: i32,
    pub display_name: String,
    pub ip_addr: String,
    pub port_start: Option<i32>, // default 30000
    pub port_end: Option<i32>,   // default 31000
    // advanced options
    pub traffic_scale: Option<f32>,       // default 1.0
    pub allow_forward: Option<bool>,      // default false
    pub allow_latency_test: Option<bool>, // default true
    pub balance_strategy: Option<i32>,
    pub forward_endpoints: Option<Vec<i32>>,
    pub allow_ipv6: Option<bool>, // default false
    pub use_forward_as_tun: Option<bool>, // default false
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct RmvServerRequest {
    pub server_id: i32,
}


#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ResetUserTrafficRequest {
    pub user_id: i32,
}
